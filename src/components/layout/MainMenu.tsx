"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";
import { useState, useRef, useEffect } from "react";

export default function MainMenu() {
    const pathname = usePathname();
    const [openDropdown, setOpenDropdown] = useState<string | null>(null);
    const timeoutRef = useRef<NodeJS.Timeout | null>(null);

    const isActive = (path: string) => {
        if (path === "/" && pathname === "/") return true;
        if (path !== "/" && pathname.startsWith(path)) return true;
        return false;
    };

    const handleMouseEnter = (dropdown: string) => {
        if (timeoutRef.current) {
            clearTimeout(timeoutRef.current);
        }
        setOpenDropdown(dropdown);
    };

    const handleMouseLeave = () => {
        timeoutRef.current = setTimeout(() => {
            setOpenDropdown(null);
        }, 150); // Small delay to prevent flickering
    };

    const handleDropdownMouseEnter = () => {
        if (timeoutRef.current) {
            clearTimeout(timeoutRef.current);
        }
    };

    const handleDropdownMouseLeave = () => {
        timeoutRef.current = setTimeout(() => {
            setOpenDropdown(null);
        }, 150);
    };

    useEffect(() => {
        return () => {
            if (timeoutRef.current) {
                clearTimeout(timeoutRef.current);
            }
        };
    }, []);

    return (
        <ul className="main-nav">
            <li>
                <Link
                    href="/"
                    className={`nav-link ${isActive("/") ? "active" : ""}`}
                >
                    Home
                </Link>
            </li>
            <li>
                <Link
                    href="/about"
                    className={`nav-link ${isActive("/about") ? "active" : ""}`}
                >
                    About
                </Link>
            </li>
            <li
                className={`dropdown-menu-parrent ${openDropdown === 'services' ? 'dropdown-open' : ''}`}
                onMouseEnter={() => handleMouseEnter('services')}
                onMouseLeave={handleMouseLeave}
            >
                <Link href="/service" className={`main1 nav-link ${isActive("/service") ? "active" : ""}`}>
                    Services <i className={`fa-solid fa-angle-down ml-1 transition-transform duration-200 ${openDropdown === 'services' ? 'rotate-180' : ''}`} />
                </Link>
                <ul
                    className={`dropdown-menu ${openDropdown === 'services' ? 'dropdown-visible' : ''}`}
                    onMouseEnter={handleDropdownMouseEnter}
                    onMouseLeave={handleDropdownMouseLeave}
                >
                    <li>
                        <Link href="/service/data-centre" className="dropdown-item">
                            <i className="fa-solid fa-server mr-2"></i>
                            Data Centre Solutions
                        </Link>
                    </li>
                    <li>
                        <Link href="/service/smart-city" className="dropdown-item">
                            <i className="fa-solid fa-city mr-2"></i>
                            Smart City Solutions
                        </Link>
                    </li>
                    <li>
                        <Link href="/service/it-consulting" className="dropdown-item">
                            <i className="fa-solid fa-lightbulb mr-2"></i>
                            IT Consulting
                        </Link>
                    </li>
                    <li>
                        <Link href="/service/software-development" className="dropdown-item">
                            <i className="fa-solid fa-code mr-2"></i>
                            Software Development
                        </Link>
                    </li>
                </ul>
            </li>
            <li
                className={`dropdown-menu-parrent ${openDropdown === 'projects' ? 'dropdown-open' : ''}`}
                onMouseEnter={() => handleMouseEnter('projects')}
                onMouseLeave={handleMouseLeave}
            >
                <Link href="/projects" className={`main1 nav-link ${isActive("/projects") ? "active" : ""}`}>
                    Projects <i className={`fa-solid fa-angle-down ml-1 transition-transform duration-200 ${openDropdown === 'projects' ? 'rotate-180' : ''}`} />
                </Link>
                <ul
                    className={`dropdown-menu ${openDropdown === 'projects' ? 'dropdown-visible' : ''}`}
                    onMouseEnter={handleDropdownMouseEnter}
                    onMouseLeave={handleDropdownMouseLeave}
                >
                    <li>
                        <Link href="/success-stories" className="dropdown-item">
                            <i className="fa-solid fa-trophy mr-2"></i>
                            Success Stories
                        </Link>
                    </li>
                    <li>
                        <Link href="/milestones" className="dropdown-item">
                            <i className="fa-solid fa-flag-checkered mr-2"></i>
                            Milestones
                        </Link>
                    </li>
                </ul>
            </li>
            <li>
                <Link
                    href="/blog"
                    className={`nav-link ${isActive("/blog") ? "active" : ""}`}
                >
                    Blog
                </Link>
            </li>
            <li>
                <Link
                    href="/contact"
                    className={`nav-link ${isActive("/contact") ? "active" : ""}`}
                >
                    Contact
                </Link>
            </li>
        </ul>
    );
}
