"use client";

import Link from "next/link";
import { useState, useEffect } from "react";
import { usePathname } from "next/navigation";

interface MobileMenuProps {
    isMobileMenu: boolean;
    handleMobileMenu: () => void;
}

export default function MobileMenu({ isMobileMenu, handleMobileMenu }: MobileMenuProps) {
    const [openSubMenus, setOpenSubMenus] = useState<{ [key: string]: boolean }>({});
    const pathname = usePathname();

    useEffect(() => {
        if (isMobileMenu) {
            handleMobileMenu();
        }
    }, [pathname]);

    useEffect(() => {
        const handleResize = () => {
            if (window.innerWidth > 991) {
                setOpenSubMenus({});
            }
        };
        window.addEventListener("resize", handleResize);
        return () => window.removeEventListener("resize", handleResize);
    }, []);

    // Handle keyboard navigation
    useEffect(() => {
        const handleKeyDown = (e: KeyboardEvent) => {
            if (e.key === 'Escape' && isMobileMenu) {
                handleMobileMenu();
            }
        };

        if (isMobileMenu) {
            document.addEventListener('keydown', handleKeyDown);
            // Prevent body scroll when menu is open
            document.body.style.overflow = 'hidden';
        } else {
            document.body.style.overflow = 'unset';
        }

        return () => {
            document.removeEventListener('keydown', handleKeyDown);
            document.body.style.overflow = 'unset';
        };
    }, [isMobileMenu, handleMobileMenu]);

    const handleToggleSubMenu = (key: string) => {
        setOpenSubMenus((prev) => ({
            ...prev,
            [key]: !prev[key],
        }));
    };

    const isHashNav = (href: string) => href === "#";

    return (
        <>
            {/* Mobile Menu Overlay */}
            <div
                className={`mobile-menu-overlay ${isMobileMenu ? "active" : ""}`}
                onClick={handleMobileMenu}
                aria-hidden="true"
            />

            {/* Mobile Sidebar Menu */}
            <div className={`mobile-sidebar d-block d-lg-none ${isMobileMenu ? "mobile-menu-active" : ""}`}>
                {/* Logo Section */}
                <div className="logo-m">
                    <Link href="/" className="flex items-center justify-center" onClick={handleMobileMenu}>
                        <img
                            src="assets/img/logo/logo_full.svg"
                            alt="Motshwanelo IT Consulting"
                            className="h-12 w-auto filter brightness-0 invert transition-all duration-300"
                        />
                    </Link>
                </div>

                {/* Close Button */}
                <button
                    className="menu-close flex items-center justify-center w-12 h-12 rounded-full bg-white/10 hover:bg-white/20 transition-all duration-300 absolute top-6 right-6 z-10"
                    onClick={handleMobileMenu}
                    aria-label="Close mobile menu"
                >
                    <i className="fa-solid fa-xmark text-white text-xl" />
                </button>
                <div className="mobile-nav">
                    <ul>
                        <li>
                            <Link
                                href="/"
                                className={`flex items-center gap-3 p-4 rounded-lg transition-all duration-300 ${pathname === "/" ? "bg-orange-500/20 text-orange-400" : "text-white/90 hover:bg-white/10"}`}
                                onClick={handleMobileMenu}
                            >
                                <i className="fa-solid fa-home text-lg"></i>
                                Home
                            </Link>
                        </li>
                        <li>
                            <Link
                                href="/about"
                                className={`flex items-center gap-3 p-4 rounded-lg transition-all duration-300 ${pathname === "/about" ? "bg-orange-500/20 text-orange-400" : "text-white/90 hover:bg-white/10"}`}
                                onClick={handleMobileMenu}
                            >
                                <i className="fa-solid fa-users text-lg"></i>
                                About Us
                            </Link>
                        </li>
                        <li className="has-dropdown">
                            <div className="menu-item-with-toggle flex items-center justify-between">
                                <Link
                                    href="/service"
                                    className={`flex items-center gap-3 p-4 rounded-lg transition-all duration-300 flex-1 ${pathname.startsWith("/service") ? "bg-orange-500/20 text-orange-400" : "text-white/90 hover:bg-white/10"}`}
                                    onClick={handleMobileMenu}
                                >
                                    <i className="fa-solid fa-cogs text-lg"></i>
                                    Services
                                </Link>
                                <button
                                    className={`submenu-button flex items-center justify-center w-10 h-10 rounded-full transition-all duration-300 mr-4 ${openSubMenus["service"] ? "bg-orange-500/20 text-orange-400 submenu-opened" : "text-white/70 hover:bg-white/10"}`}
                                    onClick={() => handleToggleSubMenu("service")}
                                    aria-label="Toggle services submenu"
                                    aria-expanded={openSubMenus["service"]}
                                >
                                    <i className={`fa-solid fa-chevron-down text-sm transition-transform duration-300 ${openSubMenus["service"] ? "rotate-180" : ""}`}></i>
                                </button>
                            </div>
                            <ul className={`sub-menu transition-all duration-300 overflow-hidden ${openSubMenus["service"] ? "max-h-96 opacity-100" : "max-h-0 opacity-0"}`}>
                                <li>
                                    <Link
                                        href="/service/data-centre"
                                        onClick={handleMobileMenu}
                                        className={`flex items-center gap-3 p-3 pl-12 rounded-lg transition-all duration-300 ${pathname === "/service/data-centre" ? "bg-orange-500/20 text-orange-400" : "text-white/80 hover:bg-white/10"}`}
                                    >
                                        <i className="fa-solid fa-server text-sm"></i>
                                        Data Centre Solutions
                                    </Link>
                                </li>
                                <li>
                                    <Link
                                        href="/service/smart-city"
                                        onClick={handleMobileMenu}
                                        className={`flex items-center gap-3 p-3 pl-12 rounded-lg transition-all duration-300 ${pathname === "/service/smart-city" ? "bg-orange-500/20 text-orange-400" : "text-white/80 hover:bg-white/10"}`}
                                    >
                                        <i className="fa-solid fa-city text-sm"></i>
                                        Smart City Solutions
                                    </Link>
                                </li>
                                <li>
                                    <Link
                                        href="/service/it-consulting"
                                        onClick={handleMobileMenu}
                                        className={`flex items-center gap-3 p-3 pl-12 rounded-lg transition-all duration-300 ${pathname === "/service/it-consulting" ? "bg-orange-500/20 text-orange-400" : "text-white/80 hover:bg-white/10"}`}
                                    >
                                        <i className="fa-solid fa-laptop-code text-sm"></i>
                                        IT Consulting
                                    </Link>
                                </li>
                                <li>
                                    <Link
                                        href="/service/software-development"
                                        onClick={handleMobileMenu}
                                        className={`flex items-center gap-3 p-3 pl-12 rounded-lg transition-all duration-300 ${pathname === "/service/software-development" ? "bg-orange-500/20 text-orange-400" : "text-white/80 hover:bg-white/10"}`}
                                    >
                                        <i className="fa-solid fa-code text-sm"></i>
                                        Software Development
                                    </Link>
                                </li>
                            </ul>
                        </li>
                        <li className="has-dropdown">
                            <div className="menu-item-with-toggle">
                                <Link
                                    href="/solutions"
                                    className={`flex items-center gap-3 p-4 rounded-lg transition-all duration-300 ${pathname.startsWith("/solutions") ? "bg-orange-500/20 text-orange-400" : "text-white/90 hover:bg-white/10"}`}
                                >
                                    <i className="fa-solid fa-lightbulb text-lg"></i>
                                    Solutions
                                </Link>
                                <span className={`submenu-button${openSubMenus["solutions"] ? " submenu-opened" : ""}`} onClick={() => handleToggleSubMenu("solutions")}>
                                    <em />
                                </span>
                            </div>
                            <ul className="sub-menu" style={{ display: openSubMenus["solutions"] ? "block" : "none" }}>
                                <li>
                                    <Link href="/solutions/digim" onClick={handleMobileMenu}>
                                        <i className="fa-solid fa-digital-tachograph mr-2"></i>
                                        DIGIM Platform
                                    </Link>
                                </li>
                                <li>
                                    <Link href="/solutions/neteco" onClick={handleMobileMenu}>
                                        <i className="fa-solid fa-network-wired mr-2"></i>
                                        NetEco Solutions
                                    </Link>
                                </li>
                                <li>
                                    <Link href="/solutions/fusion-module" onClick={handleMobileMenu}>
                                        <i className="fa-solid fa-cube mr-2"></i>
                                        FusionModule
                                    </Link>
                                </li>
                                <li>
                                    <Link href="/solutions/ups" onClick={handleMobileMenu}>
                                        <i className="fa-solid fa-battery-full mr-2"></i>
                                        UPS Solutions
                                    </Link>
                                </li>
                            </ul>
                        </li>
                        <li className="has-dropdown">
                            <div className="menu-item-with-toggle flex items-center justify-between">
                                <Link
                                    href="/projects"
                                    className={`flex items-center gap-3 p-4 rounded-lg transition-all duration-300 flex-1 ${pathname.startsWith("/projects") ? "bg-orange-500/20 text-orange-400" : "text-white/90 hover:bg-white/10"}`}
                                    onClick={handleMobileMenu}
                                >
                                    <i className="fa-solid fa-briefcase text-lg"></i>
                                    Projects
                                </Link>
                                <button
                                    className={`submenu-button flex items-center justify-center w-10 h-10 rounded-full transition-all duration-300 mr-4 ${openSubMenus["projects"] ? "bg-orange-500/20 text-orange-400 submenu-opened" : "text-white/70 hover:bg-white/10"}`}
                                    onClick={() => handleToggleSubMenu("projects")}
                                    aria-label="Toggle projects submenu"
                                    aria-expanded={openSubMenus["projects"]}
                                >
                                    <i className={`fa-solid fa-chevron-down text-sm transition-transform duration-300 ${openSubMenus["projects"] ? "rotate-180" : ""}`}></i>
                                </button>
                            </div>
                            <ul className={`sub-menu transition-all duration-300 overflow-hidden ${openSubMenus["projects"] ? "max-h-96 opacity-100" : "max-h-0 opacity-0"}`}>
                                <li>
                                    <Link
                                        href="/success-stories"
                                        onClick={handleMobileMenu}
                                        className={`flex items-center gap-3 p-3 pl-12 rounded-lg transition-all duration-300 ${pathname === "/success-stories" ? "bg-orange-500/20 text-orange-400" : "text-white/80 hover:bg-white/10"}`}
                                    >
                                        <i className="fa-solid fa-trophy text-sm"></i>
                                        Success Stories
                                    </Link>
                                </li>
                                <li>
                                    <Link
                                        href="/milestones"
                                        onClick={handleMobileMenu}
                                        className={`flex items-center gap-3 p-3 pl-12 rounded-lg transition-all duration-300 ${pathname === "/milestones" ? "bg-orange-500/20 text-orange-400" : "text-white/80 hover:bg-white/10"}`}
                                    >
                                        <i className="fa-solid fa-flag-checkered text-sm"></i>
                                        Milestones
                                    </Link>
                                </li>
                            </ul>
                        </li>
                        <li className="has-dropdown">
                            <div className="menu-item-with-toggle">
                                <Link
                                    href="/blog"
                                    className={`flex items-center gap-3 p-4 rounded-lg transition-all duration-300 ${pathname.startsWith("/blog") || pathname.startsWith("/case-studies") || pathname.startsWith("/whitepapers") ? "bg-orange-500/20 text-orange-400" : "text-white/90 hover:bg-white/10"}`}
                                >
                                    <i className="fa-solid fa-book text-lg"></i>
                                    Resources
                                </Link>
                                <span className={`submenu-button${openSubMenus["resources"] ? " submenu-opened" : ""}`} onClick={() => handleToggleSubMenu("resources")}>
                                    <em />
                                </span>
                            </div>
                            <ul className="sub-menu" style={{ display: openSubMenus["resources"] ? "block" : "none" }}>
                                <li>
                                    <Link href="/blog" onClick={handleMobileMenu}>
                                        <i className="fa-solid fa-newspaper mr-2"></i>
                                        Blog & News
                                    </Link>
                                </li>
                                <li>
                                    <Link href="/case-studies" onClick={handleMobileMenu}>
                                        <i className="fa-solid fa-chart-line mr-2"></i>
                                        Case Studies
                                    </Link>
                                </li>
                                <li>
                                    <Link href="/whitepapers" onClick={handleMobileMenu}>
                                        <i className="fa-solid fa-file-alt mr-2"></i>
                                        Whitepapers
                                    </Link>
                                </li>
                                <li>
                                    <Link href="/technology-showcase" onClick={handleMobileMenu}>
                                        <i className="fa-solid fa-display mr-2"></i>
                                        Technology Showcase
                                    </Link>
                                </li>
                            </ul>
                        </li>
                        <li>
                            <Link
                                href="/contact"
                                className={`flex items-center gap-3 p-4 rounded-lg transition-all duration-300 ${pathname === "/contact" ? "bg-orange-500/20 text-orange-400" : "text-white/90 hover:bg-white/10"}`}
                                onClick={handleMobileMenu}
                            >
                                <i className="fa-solid fa-envelope text-lg"></i>
                                Contact
                            </Link>
                        </li>
                        <li className="has-dropdown">
                            <div className="menu-item-with-toggle">
                                <Link href="#" onClick={(e) => e.preventDefault()}>
                                    Pages
                                </Link>
                                <span className={`submenu-button${openSubMenus["pages"] ? " submenu-opened" : ""}`} onClick={() => handleToggleSubMenu("pages")}>
                                    <em />
                                </span>
                            </div>
                            <ul className="sub-menu" style={{ display: openSubMenus["pages"] ? "block" : "none" }}>
                                <li>
                                    <Link href="/contact">Contact</Link>
                                </li>
                                <li>
                                    <Link href="/team">Team</Link>
                                </li>
                                <li>
                                    <Link href="/testimonial">Testimonial</Link>
                                </li>
                                <li>
                                    <Link href="/error">404</Link>
                                </li>
                            </ul>
                        </li>
                        <li className="has-dropdown">
                            <div className="menu-item-with-toggle">
                                <Link href="#" onClick={(e) => e.preventDefault()}>
                                    Blog
                                </Link>
                                <span className={`submenu-button${openSubMenus["blog"] ? " submenu-opened" : ""}`} onClick={() => handleToggleSubMenu("blog")}>
                                    <em />
                                </span>
                            </div>
                            <ul className="sub-menu" style={{ display: openSubMenus["blog"] ? "block" : "none" }}>
                                <li>
                                    <Link href="/blog">Blog</Link>
                                </li>
                                <li>
                                    <Link href="/blog-details-left">Details Left</Link>
                                </li>
                                <li>
                                    <Link href="/blog-details-right">Details Right</Link>
                                </li>
                                <li>
                                    <Link href="/blog-details">Blog Details</Link>
                                </li>
                            </ul>
                        </li>
                        <li className="has-dropdown">
                            <div className="menu-item-with-toggle">
                                <Link href="#" onClick={(e) => e.preventDefault()}>
                                    Project
                                </Link>
                                <span className={`submenu-button${openSubMenus["project"] ? " submenu-opened" : ""}`} onClick={() => handleToggleSubMenu("project")}>
                                    <em />
                                </span>
                            </div>
                            <ul className="sub-menu" style={{ display: openSubMenus["project"] ? "block" : "none" }}>
                                <li>
                                    <Link href="/projects">Project</Link>
                                </li>
                                <li>
                                    <Link href="/projects-details-left">Project Left</Link>
                                </li>
                                <li>
                                    <Link href="/projects-details-right">Project Right</Link>
                                </li>
                                <li>
                                    <Link href="/projects-details">Project Details</Link>
                                </li>
                            </ul>
                        </li>
                    </ul>

                    <div className="mobile-button">
                        <Link className="theme-btn1" href="service">
                            Learn More
                            <span>
                                <i className="fa-solid fa-arrow-right" />
                            </span>
                        </Link>
                    </div>
                    <div className="single-footer-items">
                        <h3>Contact Us</h3>
                        <div className="contact-box">
                            <div className="icon">
                                <img src="assets/img/icons/footer-icon1.png" alt="" />
                            </div>
                            <div className="pera">
                                <Link href="tel:+880123456789">+880 123 456 789</Link>
                            </div>
                        </div>
                        <div className="contact-box">
                            <div className="icon">
                                <img src="assets/img/icons/footer-icon2.png" alt="" />
                            </div>
                            <div className="pera">
                                <Link href="mailto:<EMAIL>"><EMAIL></Link>
                            </div>
                        </div>
                        <div className="contact-box">
                            <div className="icon">
                                <img src="assets/img/icons/footer-icon3.png" alt="" />
                            </div>
                            <div className="pera">
                                <Link href="tel:+880123456789">
                                    8502 Preston Rd. <br /> Inglewoo Maine 98380
                                </Link>
                            </div>
                        </div>
                    </div>
                    <div className="contact-infos">
                        <h3>Our Location</h3>
                        <ul className="social-icon">
                            <li>
                                <Link href="#">
                                    <i className="fa-brands fa-linkedin-in" />
                                </Link>
                            </li>
                            <li>
                                <Link href="#">
                                    <i className="fa-brands fa-x-twitter" />
                                </Link>
                            </li>
                            <li>
                                <Link href="#">
                                    <i className="fa-brands fa-youtube" />
                                </Link>
                            </li>
                            <li>
                                <Link href="#">
                                    <i className="fa-brands fa-instagram" />
                                </Link>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </>
    );
}
